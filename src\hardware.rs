use sysinfo::{System, SystemExt, CpuExt, DiskExt};
use std::time::{SystemTime, UNIX_EPOCH};
use crate::SystemData;

#[cfg(windows)]
use wmi::{COMLibrary, WMIConnection, Variant};

pub struct HardwareMonitor {
    system: System,
    #[cfg(windows)]
    wmi_con: Option<WMIConnection>,
}

impl HardwareMonitor {
    pub fn new() -> Self {
        let mut system = System::new_all();
        system.refresh_all();
        
        #[cfg(windows)]
        let wmi_con = Self::init_wmi().ok();
        
        Self {
            system,
            #[cfg(windows)]
            wmi_con,
        }
    }
    
    #[cfg(windows)]
    fn init_wmi() -> Result<WMIConnection, Box<dyn std::error::Error>> {
        let com_con = COMLibrary::new()?;
        let wmi_con = WMIConnection::new(com_con.into())?;
        Ok(wmi_con)
    }
    
    pub async fn get_system_data(&mut self) -> Result<SystemData, Box<dyn std::error::Error>> {
        // Refresh system info
        self.system.refresh_all();
        
        let timestamp = SystemTime::now()
            .duration_since(UNIX_EPOCH)?
            .as_secs();
        
        // Get CPU usage
        let cpu_usage = self.system.global_cpu_info().cpu_usage();
        
        // Get RAM usage
        let total_memory = self.system.total_memory();
        let used_memory = self.system.used_memory();
        let ram_usage = if total_memory > 0 {
            (used_memory as f32 / total_memory as f32) * 100.0
        } else {
            0.0
        };
        
        // Get disk usage
        let disk_usage = self.get_disk_usage();
        
        // Get temperatures
        let cpu_temp = self.get_cpu_temperature().await;
        let disk_temp = self.get_disk_temperature().await;
        
        Ok(SystemData {
            cpu_temp,
            gpu_temp: None, // TODO: Implement GPU temp
            disk_temp,
            cpu_usage,
            ram_usage,
            disk_usage,
            timestamp,
        })
    }
    
    fn get_disk_usage(&self) -> f32 {
        for disk in self.system.disks() {
            if let Some(mount_point) = disk.mount_point().to_str() {
                if mount_point == "C:\\" || mount_point == "/" {
                    let total = disk.total_space();
                    let available = disk.available_space();
                    let used = total - available;
                    
                    if total > 0 {
                        return (used as f32 / total as f32) * 100.0;
                    }
                }
            }
        }
        0.0
    }
    
    #[cfg(windows)]
    async fn get_cpu_temperature(&self) -> Option<f32> {
        if let Some(ref wmi_con) = self.wmi_con {
            // Try OpenHardwareMonitor first
            if let Ok(results) = wmi_con.raw_query("SELECT Value FROM root\\OpenHardwareMonitor\\Sensor WHERE SensorType='Temperature' AND (Name LIKE '%CPU%' OR Name LIKE '%Core%')") {
                for result in results {
                    if let Ok(value) = result.get("Value") {
                        if let Variant::R4(temp) = value {
                            return Some(*temp);
                        }
                    }
                }
            }
            
            // Try LibreHardwareMonitor
            if let Ok(results) = wmi_con.raw_query("SELECT Value FROM root\\LibreHardwareMonitor\\Sensor WHERE SensorType='Temperature' AND (Name LIKE '%CPU%' OR Name LIKE '%Core%')") {
                for result in results {
                    if let Ok(value) = result.get("Value") {
                        if let Variant::R4(temp) = value {
                            return Some(*temp);
                        }
                    }
                }
            }
        }
        
        None
    }
    
    #[cfg(not(windows))]
    async fn get_cpu_temperature(&self) -> Option<f32> {
        // For Linux/macOS, try to read from sysfs or other sources
        None
    }
    
    #[cfg(windows)]
    async fn get_disk_temperature(&self) -> Option<f32> {
        if let Some(ref wmi_con) = self.wmi_con {
            // Try OpenHardwareMonitor
            if let Ok(results) = wmi_con.raw_query("SELECT Value FROM root\\OpenHardwareMonitor\\Sensor WHERE SensorType='Temperature' AND (Name LIKE '%HDD%' OR Name LIKE '%SSD%' OR Name LIKE '%Storage%')") {
                for result in results {
                    if let Ok(value) = result.get("Value") {
                        if let Variant::R4(temp) = value {
                            return Some(*temp);
                        }
                    }
                }
            }
        }
        
        None
    }
    
    #[cfg(not(windows))]
    async fn get_disk_temperature(&self) -> Option<f32> {
        None
    }
}
