{"name": "wmi-client", "version": "0.5.0", "description": "Wrapper around the WMI client. Linux and Windows WMI clients are supported.", "main": "index.js", "scripts": {"test": "node test", "install": "node scripts/install.js"}, "repository": {"type": "git", "url": "https://github.com/R-Vision/wmi-client.git"}, "keywords": ["wmi", "wmic"], "author": "<PERSON><PERSON><PERSON> <nick.alexa<PERSON><PERSON>@gmail.com>", "license": "MIT", "bugs": {"url": "https://github.com/R-Vision/wmi-client/issues"}, "homepage": "https://github.com/R-Vision/wmi-client"}