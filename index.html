<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Temperature Monitor</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap');

        body {
            font-family: 'Poppins', sans-serif;
            background: #f8fafc;
            color: #2d3748;
            margin: 0;
            padding: 10px;
        }

        .container {
            max-width: 520px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 15px rgba(0, 0, 0, 0.06);
            padding: 22px;
            border: 1px solid #e2e8f0;
        }

        .header {
            text-align: center;
            margin-bottom: 16px;
            padding-bottom: 12px;
            border-bottom: 1px solid #e2e8f0;
        }

        .header h1 {
            color: #2d3748;
            font-size: 18px;
            font-weight: 600;
            margin: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
        }

        .pc-name {
            font-size: 12px;
            color: #718096;
            font-weight: 400;
            margin-top: 4px;
        }

        .temp-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
            margin-bottom: 14px;
        }

        .usage-row {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 10px;
            margin-bottom: 16px;
        }

        .stat-card {
            background: #f8fafc;
            border-radius: 8px;
            padding: 16px;
            border: 1px solid #e2e8f0;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .stat-icon {
            width: 24px;
            height: 24px;
            min-width: 24px;
            object-fit: contain;
        }

        .stat-content {
            flex: 1;
            text-align: left;
        }

        .stat-label {
            font-size: 12px;
            color: #718096;
            font-weight: 500;
            margin-bottom: 2px;
        }

        .stat-value {
            font-size: 20px;
            font-weight: 700;
            color: #2d3748;
        }

        .stat-normal { color: #38a169; }
        .stat-warning { color: #ed8936; }
        .stat-danger { color: #e53e3e; }
        .stat-offline { color: #a0aec0; }

        .system-status {
            background: #f8fafc;
            border-radius: 6px;
            padding: 10px;
            border: 1px solid #e2e8f0;
            margin-bottom: 16px;
            text-align: center;
        }

        .status-text {
            font-size: 12px;
            font-weight: 600;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
        }

        .controls {
            display: flex;
            gap: 8px;
            justify-content: center;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 5px;
            font-size: 12px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            flex: 1;
            font-family: 'Poppins', sans-serif;
        }

        .btn-primary {
            background: #4299e1;
            color: white;
        }

        .btn-primary:hover {
            background: #3182ce;
        }

        .btn-secondary {
            background: #e2e8f0;
            color: #4a5568;
        }

        .btn-secondary:hover {
            background: #cbd5e0;
        }



        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }



        .status-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 6px;
        }

        .status-normal { background: #38a169; }
        .status-alert { background: #e53e3e; animation: pulse 1s infinite; }
        .status-offline { background: #a0aec0; }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
        }

        .modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border-radius: 12px;
            padding: 30px;
            width: 90%;
            max-width: 500px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-header {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 20px;
            color: #4a5568;
        }

        .form-group {
            margin-bottom: 16px;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
            margin-bottom: 16px;
        }

        .form-label {
            display: block;
            margin-bottom: 4px;
            font-weight: 500;
            color: #4a5568;
            font-size: 13px;
            font-family: 'Poppins', sans-serif;
        }

        .form-input {
            width: 100%;
            padding: 8px;
            border: 1px solid #e2e8f0;
            border-radius: 5px;
            font-size: 13px;
            font-family: 'Poppins', sans-serif;
            box-sizing: border-box;
        }

        .form-input:focus {
            outline: none;
            border-color: #4299e1;
        }

        .form-checkbox {
            margin-right: 6px;
        }

        .checkbox-group {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
            padding: 6px;
            background: #f8fafc;
            border-radius: 4px;
            border: 1px solid #e2e8f0;
        }

        .checkbox-group label {
            margin: 0;
            font-size: 12px;
            color: #4a5568;
            cursor: pointer;
        }

        .alert-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 8px;
        }

        .modal-buttons {
            display: flex;
            gap: 10px;
            justify-content: flex-end;
            margin-top: 25px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><img src="icon.ico" style="width: 20px; height: 20px; margin-right: 8px;">System Monitor</h1>
            <div class="pc-name" id="pc-name-display">My PC</div>
        </div>

        <!-- Temperature Row -->
        <div class="temp-row">
            <div class="stat-card">
                <img src="icon/cpu temp.png" class="stat-icon" alt="CPU Temp">
                <div class="stat-content">
                    <div class="stat-label">CPU Temp</div>
                    <div class="stat-value stat-offline" id="cpu-temp">--°C</div>
                </div>
            </div>
            <div class="stat-card">
                <img src="icon/disk temp.png" class="stat-icon" alt="HDD Temp">
                <div class="stat-content">
                    <div class="stat-label">HDD Temp</div>
                    <div class="stat-value stat-offline" id="hdd-temp">--°C</div>
                </div>
            </div>
        </div>

        <!-- Usage Row -->
        <div class="usage-row">
            <div class="stat-card">
                <img src="icon/cpu.png" class="stat-icon" alt="CPU Usage">
                <div class="stat-content">
                    <div class="stat-label">CPU</div>
                    <div class="stat-value stat-offline" id="cpu-usage">--%</div>
                </div>
            </div>
            <div class="stat-card">
                <img src="icon/ram.png" class="stat-icon" alt="RAM Usage">
                <div class="stat-content">
                    <div class="stat-label">RAM</div>
                    <div class="stat-value stat-offline" id="ram-usage">--%</div>
                </div>
            </div>
            <div class="stat-card">
                <img src="icon/disk.png" class="stat-icon" alt="Disk Usage">
                <div class="stat-content">
                    <div class="stat-label">Disk</div>
                    <div class="stat-value stat-offline" id="hdd-usage">--%</div>
                </div>
            </div>
        </div>

        <div class="system-status">
            <div class="status-text" id="status-display">
                <span class="status-indicator status-offline" id="main-status"></span>
                <span id="status-message">System Offline</span>
            </div>
        </div>

        <div class="controls">
            <button class="btn btn-primary" id="start-btn" onclick="toggleMonitoring()">Start Monitor</button>
            <button class="btn btn-secondary" onclick="openSettings()">Settings</button>
        </div>
    </div>

    <!-- Settings Modal -->
    <div class="modal" id="settings-modal">
        <div class="modal-content">
            <div class="modal-header">Settings</div>
            
            <div class="form-row">
                <div>
                    <label class="form-label">PC Name:</label>
                    <input type="text" class="form-input" id="pc-name" placeholder="My PC">
                </div>
                <div>
                    <label class="form-label">Refresh (s):</label>
                    <input type="number" class="form-input" id="refresh-interval" min="1" max="300" value="30">
                </div>
            </div>

            <div class="form-row">
                <div>
                    <label class="form-label">Alert Cooldown (s):</label>
                    <input type="number" class="form-input" id="alert-cooldown" min="30" max="3600" value="60">
                </div>
                <div>
                    <!-- Empty div for spacing -->
                </div>
            </div>

            <div class="form-row">
                <div>
                    <label class="form-label">Telegram Bot Token:</label>
                    <input type="password" class="form-input" id="telegram-token" placeholder="Bot token">
                </div>
                <div>
                    <label class="form-label">Telegram Chat ID:</label>
                    <input type="text" class="form-input" id="telegram-chat-id" placeholder="Chat ID">
                </div>
            </div>

            <div class="form-group">
                <label class="form-label">Authorized Telegram IDs (comma separated):</label>
                <input type="text" class="form-input" id="authorized-telegram-ids" placeholder="408574191,123456789">
                <small style="color: #718096; font-size: 11px;">Chỉ những ID này mới có thể gửi lệnh đến bot. Để trống để cho phép tất cả.</small>
            </div>

            <div class="form-group">
                <label class="form-label">Alert Settings:</label>

                <div class="alert-grid">
                    <div class="checkbox-group">
                        <input type="checkbox" class="form-checkbox" id="cpu-temp-alert" checked>
                        <label for="cpu-temp-alert">CPU Temp ≥</label>
                        <input type="number" class="form-input" id="cpu-threshold" min="30" max="100" value="80" style="width: 50px; margin-left: 6px;">
                        <span style="margin-left: 2px; font-size: 11px;">°C</span>
                    </div>

                    <div class="checkbox-group">
                        <input type="checkbox" class="form-checkbox" id="hdd-temp-alert" checked>
                        <label for="hdd-temp-alert">HDD Temp ≥</label>
                        <input type="number" class="form-input" id="hdd-threshold" min="20" max="80" value="50" style="width: 50px; margin-left: 6px;">
                        <span style="margin-left: 2px; font-size: 11px;">°C</span>
                    </div>

                    <div class="checkbox-group">
                        <input type="checkbox" class="form-checkbox" id="cpu-usage-alert" checked>
                        <label for="cpu-usage-alert">CPU Usage ≥</label>
                        <input type="number" class="form-input" id="cpu-usage-threshold" min="50" max="100" value="85" style="width: 50px; margin-left: 6px;">
                        <span style="margin-left: 2px; font-size: 11px;">%</span>
                    </div>

                    <div class="checkbox-group">
                        <input type="checkbox" class="form-checkbox" id="ram-usage-alert" checked>
                        <label for="ram-usage-alert">RAM Usage ≥</label>
                        <input type="number" class="form-input" id="ram-usage-threshold" min="50" max="100" value="90" style="width: 50px; margin-left: 6px;">
                        <span style="margin-left: 2px; font-size: 11px;">%</span>
                    </div>

                    <div class="checkbox-group" style="grid-column: span 2;">
                        <input type="checkbox" class="form-checkbox" id="disk-usage-alert" checked>
                        <label for="disk-usage-alert">Disk Usage ≥</label>
                        <input type="number" class="form-input" id="disk-usage-threshold" min="70" max="100" value="95" style="width: 50px; margin-left: 6px;">
                        <span style="margin-left: 2px; font-size: 11px;">%</span>
                    </div>
                </div>
            </div>
            
            <div class="modal-buttons">
                <button class="btn btn-secondary" onclick="testTelegram()">Test Telegram</button>
                <button class="btn btn-secondary" onclick="closeSettings()">Cancel</button>
                <button class="btn btn-primary" onclick="saveSettings()">Save</button>
            </div>
        </div>
    </div>

    <script src="renderer.js"></script>
</body>
</html>
