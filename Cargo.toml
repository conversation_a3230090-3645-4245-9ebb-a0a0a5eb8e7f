[package]
name = "temp-monitor-rust"
version = "0.1.0"
edition = "2021"

[dependencies]
sysinfo = "0.30"
tokio = { version = "1.0", features = ["full"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
teloxide = { version = "0.12", features = ["macros"] }
chrono = { version = "0.4", features = ["serde"] }
wmi = "0.13"
winapi = { version = "0.3", features = ["winuser", "consoleapi"] }
eframe = "0.24"
egui = "0.24"
log = "0.4"
env_logger = "0.10"

[target.'cfg(windows)'.dependencies]
windows = { version = "0.52", features = [
    "Win32_System_Performance",
    "Win32_System_Registry",
    "Win32_Foundation",
    "Win32_System_SystemInformation"
]}
