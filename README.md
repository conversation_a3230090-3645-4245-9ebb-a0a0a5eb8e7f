# Temperature Monitor (Node.js/Electron)

Tool monitor nhiệt độ CPU và HDD với cảnh báo Telegram tự động - phiên bản Node.js tối ưu.

## Tính năng

- Monitor nhiệt độ CPU và HDD real-time với độ chính xác cao
- G<PERSON>i cảnh báo Telegram khi vượt ngưỡng (có cooldown)
- Giao diện GUI hiện đại với Electron
- C<PERSON>u hình linh hoạt (ngưỡng, thời gian refresh, Telegram)
- Log hoạt động real-time
- Hỗ trợ Windows WMI để đọc nhiệt độ chính xác

## Cài đặt

1. Cài Node.js (v16+)
2. Chạy: `npm install`
3. Chạy: `npm start`

## Cấu hình Telegram

1. Tạo bot Telegram qua @BotFather
2. Lấy Bot Token
3. L<PERSON>y Chat ID (gử<PERSON> tin nhắn cho bot rồi truy cập: `https://api.telegram.org/bot<TOKEN>/getUpdates`)
4. Nhập vào Settings trong app

## Sử dụng

1. Mở Settings để cấu hình
2. Click "Start Monitoring" để bắt đầu
3. App sẽ tự động gửi cảnh báo khi nhiệt độ vượt ngưỡng

## Ưu điểm Node.js

- Đọc nhiệt độ chính xác hơn qua systeminformation
- Giao diện đẹp hơn với Electron
- Performance tốt hơn Python
- Hỗ trợ Windows WMI native
