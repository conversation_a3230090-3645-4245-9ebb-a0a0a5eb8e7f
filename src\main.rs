use std::sync::{Arc, Mutex};
use std::time::{Duration, Instant};
use tokio::time;
use serde::{Deserialize, Serialize};

mod hardware;
mod telegram_bot;
mod gui;
mod config;

use hardware::HardwareMonitor;
use telegram_bot::TelegramBot;
use gui::MonitorApp;
use config::Config;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SystemData {
    pub cpu_temp: Option<f32>,
    pub gpu_temp: Option<f32>,
    pub disk_temp: Option<f32>,
    pub cpu_usage: f32,
    pub ram_usage: f32,
    pub disk_usage: f32,
    pub timestamp: u64,
}

impl Default for SystemData {
    fn default() -> Self {
        Self {
            cpu_temp: None,
            gpu_temp: None,
            disk_temp: None,
            cpu_usage: 0.0,
            ram_usage: 0.0,
            disk_usage: 0.0,
            timestamp: 0,
        }
    }
}

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    env_logger::init();
    
    // Load config
    let config = Config::load().unwrap_or_default();
    let config = Arc::new(Mutex::new(config));
    
    // Shared system data
    let system_data = Arc::new(Mutex::new(SystemData::default()));
    
    // Hardware monitor
    let mut hardware_monitor = HardwareMonitor::new();
    
    // Telegram bot (optional)
    let telegram_bot = if let Ok(config_lock) = config.lock() {
        if !config_lock.telegram_token.is_empty() {
            Some(TelegramBot::new(
                config_lock.telegram_token.clone(),
                config_lock.authorized_telegram_ids.clone(),
            ).await?)
        } else {
            None
        }
    } else {
        None
    };
    
    // Clone for monitoring task
    let system_data_clone = Arc::clone(&system_data);
    let config_clone = Arc::clone(&config);
    
    // Start monitoring task
    tokio::spawn(async move {
        let mut interval = time::interval(Duration::from_secs(30)); // 30s interval
        
        loop {
            interval.tick().await;
            
            // Get system data
            if let Ok(data) = hardware_monitor.get_system_data().await {
                // Update shared data
                if let Ok(mut system_data_lock) = system_data_clone.lock() {
                    *system_data_lock = data.clone();
                }
                
                // Check alerts
                if let (Ok(config_lock), Some(ref bot)) = (config_clone.lock(), &telegram_bot) {
                    if should_send_alert(&data, &config_lock) {
                        let message = format_alert_message(&data, &config_lock.pc_name);
                        if let Err(e) = bot.send_alert(&config_lock.telegram_chat_id, &message).await {
                            log::error!("Failed to send alert: {}", e);
                        }
                    }
                }
            }
        }
    });
    
    // Start GUI
    let options = eframe::NativeOptions {
        viewport: egui::ViewportBuilder::default()
            .with_inner_size([560.0, 450.0])
            .with_resizable(false),
        ..Default::default()
    };
    
    eframe::run_native(
        "Temperature Monitor - Rust",
        options,
        Box::new(|_cc| Box::new(MonitorApp::new(system_data, config, telegram_bot))),
    )?;
    
    Ok(())
}

fn should_send_alert(data: &SystemData, config: &Config) -> bool {
    if let Some(cpu_temp) = data.cpu_temp {
        if config.cpu_temp_alert && cpu_temp >= config.cpu_threshold {
            return true;
        }
    }
    
    if data.cpu_usage >= config.cpu_usage_threshold {
        return true;
    }
    
    if data.ram_usage >= config.ram_usage_threshold {
        return true;
    }
    
    false
}

fn format_alert_message(data: &SystemData, pc_name: &str) -> String {
    let cpu_temp = data.cpu_temp.map_or("N/A".to_string(), |t| format!("{:.1}°C", t));
    let disk_temp = data.disk_temp.map_or("N/A".to_string(), |t| format!("{:.1}°C", t));
    
    format!(
        "🚨 Alert 💻 {} | {}\nTemp: CPU {} | Disk {}\nUsage: CPU {:.0}% | RAM {:.0}% | Disk {:.0}%",
        pc_name,
        chrono::Utc::now().format("%Y-%m-%d %H:%M:%S"),
        cpu_temp,
        disk_temp,
        data.cpu_usage,
        data.ram_usage,
        data.disk_usage
    )
}
