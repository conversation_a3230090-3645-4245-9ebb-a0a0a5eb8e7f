{"name": "temp-monitor", "version": "1.0.0", "description": "Temperature Monitor with Telegram alerts", "main": "main.js", "author": "TempMonitor", "scripts": {"start": "electron .", "build": "npx electron-packager . TempMonitor --platform=win32 --arch=x64 --out=build --overwrite --icon=icon.ico"}, "dependencies": {"systeminformation": "^5.21.20", "node-telegram-bot-api": "^0.64.0", "wmi-client": "^0.5.0"}, "devDependencies": {"electron": "^27.0.0"}}