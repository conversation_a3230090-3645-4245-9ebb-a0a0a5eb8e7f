use serde::{Deserialize, Serialize};
use std::fs;
use std::path::Path;

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct Config {
    pub pc_name: String,
    pub telegram_token: String,
    pub telegram_chat_id: String,
    pub authorized_telegram_ids: Vec<String>,
    pub refresh_interval: u64,
    pub cpu_threshold: f32,
    pub hdd_threshold: f32,
    pub cpu_usage_threshold: f32,
    pub ram_usage_threshold: f32,
    pub disk_usage_threshold: f32,
    pub cpu_temp_alert: bool,
    pub hdd_temp_alert: bool,
    pub cpu_usage_alert: bool,
    pub ram_usage_alert: bool,
    pub disk_usage_alert: bool,
    pub alert_cooldown: u64,
}

impl Default for Config {
    fn default() -> Self {
        Self {
            pc_name: "My PC".to_string(),
            telegram_token: String::new(),
            telegram_chat_id: String::new(),
            authorized_telegram_ids: Vec::new(),
            refresh_interval: 30,
            cpu_threshold: 80.0,
            hdd_threshold: 50.0,
            cpu_usage_threshold: 85.0,
            ram_usage_threshold: 90.0,
            disk_usage_threshold: 95.0,
            cpu_temp_alert: true,
            hdd_temp_alert: true,
            cpu_usage_alert: true,
            ram_usage_alert: true,
            disk_usage_alert: true,
            alert_cooldown: 60,
        }
    }
}

impl Config {
    pub fn load() -> Result<Self, Box<dyn std::error::Error>> {
        let config_path = "config.json";
        
        if Path::new(config_path).exists() {
            let content = fs::read_to_string(config_path)?;
            let config: Config = serde_json::from_str(&content)?;
            Ok(config)
        } else {
            let default_config = Config::default();
            default_config.save()?;
            Ok(default_config)
        }
    }
    
    pub fn save(&self) -> Result<(), Box<dyn std::error::Error>> {
        let content = serde_json::to_string_pretty(self)?;
        fs::write("config.json", content)?;
        Ok(())
    }
}
