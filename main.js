const { app, BrowserWindow, ipcMain } = require('electron');
const path = require('path');
const fs = require('fs');
const si = require('systeminformation');
const TelegramBot = require('node-telegram-bot-api');

class TempMonitor {
    constructor() {
        this.mainWindow = null;
        this.config = this.loadConfig();
        this.monitoring = false;
        this.monitorInterval = null;
        this.bot = null;
        this.lastAlertTime = {};
        
        this.createWindow();
        this.setupIPC();
    }

    loadConfig() {
        const configPath = path.join(__dirname, 'config.json');
        const defaultConfig = {
            pc_name: 'My PC',
            telegram_token: '',
            telegram_chat_id: '',
            authorized_telegram_ids: [], // Danh sách ID Telegram được phép gửi lệnh
            refresh_interval: 30,
            cpu_threshold: 80,
            hdd_threshold: 50,
            cpu_usage_threshold: 85,
            ram_usage_threshold: 90,
            disk_usage_threshold: 95,
            cpu_temp_alert: true,
            hdd_temp_alert: true,
            cpu_usage_alert: true,
            ram_usage_alert: true,
            disk_usage_alert: true,
            alert_cooldown: 60 // 1 minute
        };

        try {
            if (fs.existsSync(configPath)) {
                return { ...defaultConfig, ...JSON.parse(fs.readFileSync(configPath, 'utf8')) };
            }
        } catch (error) {
            console.error('Error loading config:', error);
        }
        
        return defaultConfig;
    }

    saveConfig() {
        const configPath = path.join(__dirname, 'config.json');
        try {
            fs.writeFileSync(configPath, JSON.stringify(this.config, null, 4));
        } catch (error) {
            console.error('Error saving config:', error);
        }
    }

    createWindow() {
        this.mainWindow = new BrowserWindow({
            width: 560,
            height: 450,
            webPreferences: {
                nodeIntegration: true,
                contextIsolation: false
            },
            resizable: false,
            icon: path.join(__dirname, 'icon.ico'),
            show: false
        });

        this.mainWindow.loadFile('index.html');
        this.mainWindow.setMenuBarVisibility(false);

        this.mainWindow.once('ready-to-show', () => {
            this.mainWindow.show();
        });

        // Check if OpenHardwareMonitor is running
        this.checkOHMStatus();
    }

    setupIPC() {
        ipcMain.handle('get-config', () => this.config);
        
        ipcMain.handle('save-config', (event, newConfig) => {
            this.config = { ...this.config, ...newConfig };
            this.saveConfig();

            // Reload config from file to ensure consistency
            this.config = this.loadConfig();

            this.setupTelegramBot();
            return true;
        });

        ipcMain.handle('start-monitoring', () => {
            this.startMonitoring();
            return true;
        });

        ipcMain.handle('stop-monitoring', () => {
            this.stopMonitoring();
            return true;
        });

        ipcMain.handle('test-telegram', async (event, token, chatId) => {
            try {
                const testBot = new TelegramBot(token);
                const pcName = this.config.pc_name || 'My PC';
                await testBot.sendMessage(chatId, `🔧 Test message from ${pcName} Temperature Monitor`);
                return { success: true };
            } catch (error) {
                return { success: false, error: error.message };
            }
        });



        ipcMain.handle('get-temperatures', async () => {
            return await this.getTemperatures();
        });
    }

    setupTelegramBot() {
        // Stop existing bot first
        if (this.bot) {
            try {
                this.bot.stopPolling();
                this.bot.removeAllListeners();
            } catch (error) {
                console.log('Error stopping previous bot:', error);
            }
            this.bot = null;
        }

        if (this.config.telegram_token && this.config.telegram_chat_id) {
            try {
                this.bot = new TelegramBot(this.config.telegram_token, {
                    polling: {
                        interval: 1000,
                        autoStart: true,
                        params: {
                            timeout: 10
                        }
                    }
                });

                // Setup commands
                this.setupTelegramCommands();

                console.log('✅ Telegram bot initialized - COMMANDS + ALERTS');
            } catch (error) {
                console.error('❌ Error setting up Telegram bot:', error);
                this.bot = null;
            }
        } else {
            console.log('❌ No Telegram token/chat_id configured');
            this.bot = null;
        }
    }

    isAuthorizedUser(chatId) {
        if (!this.config.authorized_telegram_ids || this.config.authorized_telegram_ids.length === 0) {
            return true;
        }
        return this.config.authorized_telegram_ids.includes(chatId.toString());
    }

    setupTelegramCommands() {
        if (!this.bot) return;

        console.log('🤖 Setting up Telegram commands...');

        // Set menu với 1 command
        this.bot.setMyCommands([
            { command: 'start', description: 'Get system status' }
        ]).then(() => {
            console.log('✅ Bot menu set: /start');
        }).catch(err => {
            console.log('❌ Error setting menu:', err);
        });

        // Handle /start command
        this.bot.onText(/\/start/, async (msg) => {
            const chatId = msg.chat.id;
            console.log(`📱 Received /start from chat ${chatId}`);

            // Kiểm tra quyền truy cập
            if (!this.isAuthorizedUser(chatId)) {
                console.log(`🚫 Unauthorized access attempt from chat ${chatId}`);
                await this.bot.sendMessage(chatId, '🚫 Bạn không có quyền sử dụng bot này.');
                return;
            }

            try {
                const temps = await this.getTemperatures();
                const pcName = this.config.pc_name || 'My PC';
                const time = new Date().toLocaleString();

                let message = `💻 ${pcName} | ${time}\n`;

                // Temperature line with status icons
                const cpuTempIcon = temps.cpu !== null && temps.cpu >= this.config.cpu_threshold ? '🔴' : '✅';
                const diskTempIcon = temps.hdd !== null && temps.hdd >= this.config.hdd_threshold ? '🔴' : '✅';
                const cpuTemp = temps.cpu !== null ? `${temps.cpu.toFixed(1)}°C` : 'N/A';
                const diskTemp = temps.hdd !== null ? `${temps.hdd.toFixed(1)}°C` : 'N/A';
                message += `Temp: ${cpuTempIcon} CPU ${cpuTemp} | ${diskTempIcon} Disk ${diskTemp}\n`;

                // Usage line with status icons
                const cpuUsageIcon = temps.cpuUsage >= this.config.cpu_usage_threshold ? '🔴' : '✅';
                const ramUsageIcon = temps.ramUsage >= this.config.ram_usage_threshold ? '🔴' : '✅';
                const diskUsageIcon = temps.hddUsage >= this.config.disk_usage_threshold ? '🔴' : '✅';
                message += `Usage: ${cpuUsageIcon} CPU ${temps.cpuUsage}% | ${ramUsageIcon} RAM ${temps.ramUsage}% | ${diskUsageIcon} Disk ${temps.hddUsage}%`;

                console.log('📤 Sending status:', message);
                await this.bot.sendMessage(chatId, message);
                console.log('✅ Status sent successfully');
            } catch (error) {
                console.error('❌ Error getting/sending status:', error);
                this.bot.sendMessage(chatId, '❌ Error getting system status');
            }
        });
    }





    async getTemperatures() {
        try {
            const [cpuTemp, diskTemp, systemInfo] = await Promise.all([
                this.getCPUTemperature(),
                this.getDiskTemperature(),
                this.getSystemInfo()
            ]);

            return {
                cpu: cpuTemp,
                hdd: diskTemp,
                cpuUsage: systemInfo.cpuUsage,
                ramUsage: systemInfo.ramUsage,
                hddUsage: systemInfo.hddUsage,
                timestamp: new Date().toISOString()
            };
        } catch (error) {
            console.error('Error getting temperatures:', error);
            return {
                cpu: null,
                hdd: null,
                cpuUsage: null,
                ramUsage: null,
                hddUsage: null,
                timestamp: new Date().toISOString()
            };
        }
    }

    async getCPUTemperature() {
        try {
            const { exec } = require('child_process');

            // Try LibreHardwareMonitor first (newer fork)
            const libreTemp = await new Promise((resolve) => {
                exec('powershell "Get-WmiObject -Namespace root/LibreHardwareMonitor -Class Sensor | Where-Object {$_.SensorType -eq \'Temperature\' -and ($_.Name -like \'*CPU*\' -or $_.Name -like \'*Core*\')} | Select-Object -First 1 Value"',
                    (error, stdout) => {
                        if (error) {
                            resolve(null);
                            return;
                        }
                        const match = stdout.match(/(\d+\.?\d*)/);
                        resolve(match ? parseFloat(match[1]) : null);
                    });
            });

            if (libreTemp !== null) return libreTemp;

            // Try OpenHardwareMonitor WMI
            const ohwmTemp = await new Promise((resolve) => {
                exec('powershell "Get-WmiObject -Namespace root/OpenHardwareMonitor -Class Sensor | Where-Object {$_.SensorType -eq \'Temperature\' -and ($_.Name -like \'*CPU*\' -or $_.Name -like \'*Core*\')} | Select-Object -First 1 Value"',
                    (error, stdout) => {
                        if (error) {
                            resolve(null);
                            return;
                        }
                        const match = stdout.match(/(\d+\.?\d*)/);
                        resolve(match ? parseFloat(match[1]) : null);
                    });
            });

            if (ohwmTemp !== null) return ohwmTemp;

            // Try WMIC thermal zone
            const wmicTemp = await new Promise((resolve) => {
                exec('wmic /namespace:\\\\root\\wmi PATH MSAcpi_ThermalZoneTemperature get CurrentTemperature /value',
                    (error, stdout) => {
                        if (error) {
                            resolve(null);
                            return;
                        }
                        const match = stdout.match(/CurrentTemperature=(\d+)/);
                        if (match) {
                            // Convert from Kelvin to Celsius (divide by 10 then subtract 273.15)
                            const tempKelvin = parseInt(match[1]) / 10;
                            const tempCelsius = tempKelvin - 273.15;
                            resolve(tempCelsius > 0 && tempCelsius < 150 ? tempCelsius : null);
                        } else {
                            resolve(null);
                        }
                    });
            });

            if (wmicTemp !== null) return wmicTemp;

            // Fallback to systeminformation
            const data = await si.cpuTemperature();
            return data.main || data.cores?.[0] || null;
        } catch (error) {
            console.error('Error getting CPU temperature:', error);
            return null;
        }
    }

    async getDiskTemperature() {
        try {
            const { exec } = require('child_process');

            // Try LibreHardwareMonitor first
            const libreTemp = await new Promise((resolve) => {
                exec('powershell "Get-WmiObject -Namespace root/LibreHardwareMonitor -Class Sensor | Where-Object {$_.SensorType -eq \'Temperature\' -and ($_.Name -like \'*HDD*\' -or $_.Name -like \'*SSD*\' -or $_.Name -like \'*Storage*\' -or $_.Name -like \'*Drive*\')} | Select-Object -First 1 Value"',
                    (error, stdout) => {
                        if (error) {
                            resolve(null);
                            return;
                        }
                        const match = stdout.match(/(\d+\.?\d*)/);
                        resolve(match ? parseFloat(match[1]) : null);
                    });
            });

            if (libreTemp !== null) return libreTemp;

            // Try OpenHardwareMonitor WMI
            const ohwmTemp = await new Promise((resolve) => {
                exec('powershell "Get-WmiObject -Namespace root/OpenHardwareMonitor -Class Sensor | Where-Object {$_.SensorType -eq \'Temperature\' -and ($_.Name -like \'*HDD*\' -or $_.Name -like \'*SSD*\' -or $_.Name -like \'*Storage*\' -or $_.Name -like \'*Drive*\')} | Select-Object -First 1 Value"',
                    (error, stdout) => {
                        if (error) {
                            resolve(null);
                            return;
                        }
                        const match = stdout.match(/(\d+\.?\d*)/);
                        resolve(match ? parseFloat(match[1]) : null);
                    });
            });

            if (ohwmTemp !== null) return ohwmTemp;

            // Try systeminformation
            const diskLayout = await si.diskLayout();
            for (const disk of diskLayout) {
                if (disk.temperature && disk.temperature > 0) {
                    return disk.temperature;
                }
            }

            // Fallback: try Windows SMART data
            if (process.platform === 'win32') {
                return await this.getWindowsDiskTemp();
            }

            return null;
        } catch (error) {
            console.error('Error getting disk temperature:', error);
            return null;
        }
    }

    async getWindowsDiskTemp() {
        try {
            const { exec } = require('child_process');
            return new Promise((resolve) => {
                // Try SMART temperature data
                exec('powershell "Get-WmiObject -Namespace root/wmi -Class MSStorageDriver_FailurePredictTemperature | Select-Object CurrentTemperature"',
                    (error, stdout) => {
                        if (error) {
                            // Try alternative WMIC approach
                            exec('wmic diskdrive get temperature /value', (error2, stdout2) => {
                                if (error2) {
                                    resolve(null);
                                    return;
                                }
                                const match = stdout2.match(/temperature=(\d+)/i);
                                resolve(match ? parseInt(match[1]) : null);
                            });
                            return;
                        }

                        const match = stdout.match(/(\d+)/);
                        if (match) {
                            // Convert from Kelvin to Celsius (subtract 273)
                            const tempKelvin = parseInt(match[1]);
                            const tempCelsius = tempKelvin - 273;
                            resolve(tempCelsius > 0 && tempCelsius < 100 ? tempCelsius : null);
                        } else {
                            resolve(null);
                        }
                    });
            });
        } catch (error) {
            return null;
        }
    }

    async getSystemInfo() {
        try {
            const [cpu, mem, fsSize] = await Promise.all([
                si.currentLoad(),
                si.mem(),
                si.fsSize()
            ]);

            // CPU Usage
            const cpuUsage = Math.round(cpu.currentLoad);

            // RAM Usage
            const ramUsage = Math.round((mem.used / mem.total) * 100);

            // HDD Usage (primary drive)
            let hddUsage = null;
            if (fsSize && fsSize.length > 0) {
                const primaryDrive = fsSize.find(drive => drive.mount === 'C:' || drive.mount === '/') || fsSize[0];
                hddUsage = Math.round(primaryDrive.use);
            }

            return {
                cpuUsage,
                ramUsage,
                hddUsage
            };
        } catch (error) {
            console.error('Error getting system info:', error);
            return {
                cpuUsage: null,
                ramUsage: null,
                hddUsage: null
            };
        }
    }

    startMonitoring() {
        if (this.monitoring) return;

        this.monitoring = true;

        // Only setup bot if not already setup
        if (!this.bot && this.config.telegram_token && this.config.telegram_chat_id) {
            this.setupTelegramBot();
        }

        this.monitorInterval = setInterval(async () => {
            const temps = await this.getTemperatures();

            // Send to renderer
            if (this.mainWindow) {
                this.mainWindow.webContents.send('temperature-update', temps);
            }

            // Check for alerts
            await this.checkAlerts(temps);

        }, this.config.refresh_interval * 1000);

        console.log('Monitoring started');
    }

    stopMonitoring() {
        if (!this.monitoring) return;

        this.monitoring = false;

        if (this.monitorInterval) {
            clearInterval(this.monitorInterval);
            this.monitorInterval = null;
        }

        console.log('Monitoring stopped');
    }

    async checkOHMStatus() {
        try {
            const { exec } = require('child_process');

            // Check if OpenHardwareMonitor process is running
            exec('tasklist /FI "IMAGENAME eq OpenHardwareMonitor.exe"', (error, stdout) => {
                if (stdout.includes('OpenHardwareMonitor.exe')) {
                    console.log('✅ OpenHardwareMonitor is running');
                } else {
                    console.log('⚠️  OpenHardwareMonitor not detected. Temperature readings may not work.');
                    console.log('   Please download and run OpenHardwareMonitor v0.9.6 from:');
                    console.log('   https://openhardwaremonitor.org/downloads/');
                }
            });

            // Check LibreHardwareMonitor as alternative
            exec('tasklist /FI "IMAGENAME eq LibreHardwareMonitor.exe"', (error, stdout) => {
                if (stdout.includes('LibreHardwareMonitor.exe')) {
                    console.log('✅ LibreHardwareMonitor is running');
                }
            });

        } catch (error) {
            console.error('Error checking OHM status:', error);
        }
    }

    async checkAlerts(temps) {
        console.log('🔍 Checking alerts...');
        console.log('🤖 Bot exists:', !!this.bot);
        console.log('💬 Chat ID:', this.config.telegram_chat_id);

        if (!this.bot || !this.config.telegram_chat_id) {
            console.log('❌ Alert check failed: No bot or chat ID');
            return;
        }

        const now = Date.now();
        const alerts = [];

        console.log('📊 Current temps:', temps);
        console.log('⚙️ Thresholds:', {
            cpu: this.config.cpu_threshold,
            hdd: this.config.hdd_threshold,
            cpu_usage: this.config.cpu_usage_threshold,
            ram_usage: this.config.ram_usage_threshold,
            disk_usage: this.config.disk_usage_threshold
        });

        // Check CPU temperature
        if (this.config.cpu_temp_alert && temps.cpu && temps.cpu >= this.config.cpu_threshold) {
            const lastAlert = this.lastAlertTime.cpu || 0;
            const timeSinceLastAlert = (now - lastAlert) / 1000;
            console.log(`🌡️ CPU ${temps.cpu.toFixed(1)}°C >= ${this.config.cpu_threshold}°C, last alert: ${timeSinceLastAlert.toFixed(0)}s ago (cooldown: ${this.config.alert_cooldown}s)`);

            if (now - lastAlert > this.config.alert_cooldown * 1000) {
                alerts.push(`CPU Temp: ${temps.cpu.toFixed(1)}°C`);
                this.lastAlertTime.cpu = now;
                console.log(`🚨 CPU temp alert triggered!`);
            } else {
                console.log(`⏳ CPU alert in cooldown (${(this.config.alert_cooldown - timeSinceLastAlert).toFixed(0)}s remaining)`);
            }
        }

        // Check HDD temperature
        if (this.config.hdd_temp_alert && temps.hdd && temps.hdd >= this.config.hdd_threshold) {
            const lastAlert = this.lastAlertTime.hdd || 0;
            if (now - lastAlert > this.config.alert_cooldown * 1000) {
                alerts.push(`HDD Temp: ${temps.hdd.toFixed(1)}°C`);
                this.lastAlertTime.hdd = now;
            }
        }

        // Check CPU usage
        if (this.config.cpu_usage_alert && temps.cpuUsage && temps.cpuUsage >= this.config.cpu_usage_threshold) {
            const lastAlert = this.lastAlertTime.cpuUsage || 0;
            if (now - lastAlert > this.config.alert_cooldown * 1000) {
                alerts.push(`CPU Usage: ${temps.cpuUsage}%`);
                this.lastAlertTime.cpuUsage = now;
            }
        }

        // Check RAM usage
        if (this.config.ram_usage_alert && temps.ramUsage && temps.ramUsage >= this.config.ram_usage_threshold) {
            const lastAlert = this.lastAlertTime.ramUsage || 0;
            const timeSinceLastAlert = (now - lastAlert) / 1000;
            console.log(`🧠 RAM ${temps.ramUsage}% >= ${this.config.ram_usage_threshold}%, last alert: ${timeSinceLastAlert.toFixed(0)}s ago`);

            if (now - lastAlert > this.config.alert_cooldown * 1000) {
                alerts.push(`RAM Usage: ${temps.ramUsage}%`);
                this.lastAlertTime.ramUsage = now;
                console.log(`🚨 RAM usage alert triggered!`);
            } else {
                console.log(`⏳ RAM alert in cooldown (${(this.config.alert_cooldown - timeSinceLastAlert).toFixed(0)}s remaining)`);
            }
        }

        // Check Disk usage
        if (this.config.disk_usage_alert && temps.hddUsage && temps.hddUsage >= this.config.disk_usage_threshold) {
            const lastAlert = this.lastAlertTime.diskUsage || 0;
            if (now - lastAlert > this.config.alert_cooldown * 1000) {
                alerts.push(`Disk Usage: ${temps.hddUsage}%`);
                this.lastAlertTime.diskUsage = now;
            }
        }

        // Send alerts
        if (alerts.length > 0) {
            console.log('🚨 ALERT TRIGGERED! Alerts:', alerts);

            if (!this.bot) {
                console.error('❌ Bot not initialized!');
                return;
            }

            if (!this.config.telegram_chat_id) {
                console.error('❌ No chat ID configured!');
                return;
            }

            const pcName = this.config.pc_name || 'PC';
            const time = new Date().toLocaleString();

            // Get current temps for full status
            const currentTemps = await this.getTemperatures();

            let message = `🚨 Alert 💻 ${pcName} | ${time}\n`;

            // Temperature line with status icons
            const cpuTempIcon = currentTemps.cpu !== null && currentTemps.cpu >= this.config.cpu_threshold ? '🔴' : '✅';
            const diskTempIcon = currentTemps.hdd !== null && currentTemps.hdd >= this.config.hdd_threshold ? '🔴' : '✅';
            const cpuTemp = currentTemps.cpu !== null ? `${currentTemps.cpu.toFixed(1)}°C` : 'N/A';
            const diskTemp = currentTemps.hdd !== null ? `${currentTemps.hdd.toFixed(1)}°C` : 'N/A';
            message += `Temp: ${cpuTempIcon} CPU ${cpuTemp} | ${diskTempIcon} Disk ${diskTemp}\n`;

            // Usage line with status icons
            const cpuUsageIcon = currentTemps.cpuUsage >= this.config.cpu_usage_threshold ? '🔴' : '✅';
            const ramUsageIcon = currentTemps.ramUsage >= this.config.ram_usage_threshold ? '🔴' : '✅';
            const diskUsageIcon = currentTemps.hddUsage >= this.config.disk_usage_threshold ? '🔴' : '✅';
            message += `Usage: ${cpuUsageIcon} CPU ${currentTemps.cpuUsage}% | ${ramUsageIcon} RAM ${currentTemps.ramUsage}% | ${diskUsageIcon} Disk ${currentTemps.hddUsage}%`;

            console.log('📱 Sending message:', message);
            console.log('📱 To chat ID:', this.config.telegram_chat_id);

            try {
                await this.bot.sendMessage(this.config.telegram_chat_id, message);
                console.log('✅ Alert sent successfully!');
            } catch (error) {
                console.error('❌ Error sending Telegram alert:', error);
                console.error('❌ Error details:', error.message);
            }
        }
    }
}

app.whenReady().then(() => {
    new TempMonitor();
});

app.on('window-all-closed', () => {
    if (process.platform !== 'darwin') {
        app.quit();
    }
});

app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
        new TempMonitor();
    }
});
