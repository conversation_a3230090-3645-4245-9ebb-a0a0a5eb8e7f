use eframe::egui;
use std::sync::{Arc, Mutex};
use crate::{SystemData, Config};
use crate::telegram_bot::TelegramBot;

pub struct MonitorApp {
    system_data: Arc<Mutex<SystemData>>,
    config: Arc<Mutex<Config>>,
    telegram_bot: Option<TelegramBot>,
    monitoring: bool,
    show_settings: bool,
}

impl MonitorApp {
    pub fn new(
        system_data: Arc<Mutex<SystemData>>,
        config: Arc<Mutex<Config>>,
        telegram_bot: Option<TelegramBot>,
    ) -> Self {
        Self {
            system_data,
            config,
            telegram_bot,
            monitoring: false,
            show_settings: false,
        }
    }
}

impl eframe::App for MonitorApp {
    fn update(&mut self, ctx: &egui::Context, _frame: &mut eframe::Frame) {
        // Request repaint every second
        ctx.request_repaint_after(std::time::Duration::from_secs(1));
        
        egui::CentralPanel::default().show(ctx, |ui| {
            ui.heading("🦀 Temperature Monitor - Rust Edition");
            
            if let Ok(config) = self.config.lock() {
                ui.label(format!("PC: {}", config.pc_name));
            }
            
            ui.separator();
            
            // Display system data
            if let Ok(data) = self.system_data.lock() {
                ui.horizontal(|ui| {
                    ui.group(|ui| {
                        ui.vertical(|ui| {
                            ui.label("🌡️ CPU Temp");
                            let temp_text = data.cpu_temp
                                .map_or("--°C".to_string(), |t| format!("{:.1}°C", t));
                            let color = if data.cpu_temp.unwrap_or(0.0) >= 80.0 {
                                egui::Color32::RED
                            } else {
                                egui::Color32::GREEN
                            };
                            ui.colored_label(color, temp_text);
                        });
                    });
                    
                    ui.group(|ui| {
                        ui.vertical(|ui| {
                            ui.label("💾 Disk Temp");
                            let temp_text = data.disk_temp
                                .map_or("--°C".to_string(), |t| format!("{:.1}°C", t));
                            let color = if data.disk_temp.unwrap_or(0.0) >= 50.0 {
                                egui::Color32::RED
                            } else {
                                egui::Color32::GREEN
                            };
                            ui.colored_label(color, temp_text);
                        });
                    });
                });
                
                ui.separator();
                
                ui.horizontal(|ui| {
                    ui.group(|ui| {
                        ui.vertical(|ui| {
                            ui.label("🖥️ CPU Usage");
                            let color = if data.cpu_usage >= 85.0 {
                                egui::Color32::RED
                            } else {
                                egui::Color32::GREEN
                            };
                            ui.colored_label(color, format!("{:.1}%", data.cpu_usage));
                        });
                    });
                    
                    ui.group(|ui| {
                        ui.vertical(|ui| {
                            ui.label("🧠 RAM Usage");
                            let color = if data.ram_usage >= 90.0 {
                                egui::Color32::RED
                            } else {
                                egui::Color32::GREEN
                            };
                            ui.colored_label(color, format!("{:.1}%", data.ram_usage));
                        });
                    });
                    
                    ui.group(|ui| {
                        ui.vertical(|ui| {
                            ui.label("💿 Disk Usage");
                            let color = if data.disk_usage >= 95.0 {
                                egui::Color32::RED
                            } else {
                                egui::Color32::GREEN
                            };
                            ui.colored_label(color, format!("{:.1}%", data.disk_usage));
                        });
                    });
                });
            }
            
            ui.separator();
            
            // Status
            let status_text = if self.monitoring {
                "🟢 Monitoring Active"
            } else {
                "🔴 Monitoring Stopped"
            };
            ui.label(status_text);
            
            ui.separator();
            
            // Controls
            ui.horizontal(|ui| {
                let button_text = if self.monitoring {
                    "Stop Monitoring"
                } else {
                    "Start Monitoring"
                };
                
                if ui.button(button_text).clicked() {
                    self.monitoring = !self.monitoring;
                }
                
                if ui.button("Settings").clicked() {
                    self.show_settings = true;
                }
            });
        });
        
        // Settings window
        if self.show_settings {
            egui::Window::new("Settings")
                .collapsible(false)
                .resizable(false)
                .show(ctx, |ui| {
                    if let Ok(mut config) = self.config.lock() {
                        ui.horizontal(|ui| {
                            ui.label("PC Name:");
                            ui.text_edit_singleline(&mut config.pc_name);
                        });
                        
                        ui.horizontal(|ui| {
                            ui.label("Telegram Token:");
                            ui.text_edit_singleline(&mut config.telegram_token);
                        });
                        
                        ui.horizontal(|ui| {
                            ui.label("Chat ID:");
                            ui.text_edit_singleline(&mut config.telegram_chat_id);
                        });
                        
                        ui.horizontal(|ui| {
                            ui.label("CPU Threshold:");
                            ui.add(egui::Slider::new(&mut config.cpu_threshold, 30.0..=100.0).suffix("°C"));
                        });
                        
                        ui.horizontal(|ui| {
                            ui.label("CPU Usage Threshold:");
                            ui.add(egui::Slider::new(&mut config.cpu_usage_threshold, 50.0..=100.0).suffix("%"));
                        });
                        
                        ui.separator();
                        
                        ui.horizontal(|ui| {
                            if ui.button("Save").clicked() {
                                let _ = config.save();
                                self.show_settings = false;
                            }
                            
                            if ui.button("Cancel").clicked() {
                                self.show_settings = false;
                            }
                        });
                    }
                });
        }
    }
}
