use teloxide::{<PERSON><PERSON>, RequestError};
use teloxide::types::{ChatId, Message};
use teloxide::requests::Requester;
use std::collections::HashMap;
use std::time::{SystemTime, UNIX_EPOCH};

pub struct TelegramBot {
    bot: Bot,
    authorized_ids: Vec<String>,
    last_alert_time: HashMap<String, u64>,
}

impl TelegramBot {
    pub async fn new(
        token: String,
        authorized_ids: Vec<String>,
    ) -> Result<Self, Box<dyn std::error::Error>> {
        let bot = Bot::new(token);
        
        // Test connection
        bot.get_me().await?;
        
        Ok(Self {
            bot,
            authorized_ids,
            last_alert_time: HashMap::new(),
        })
    }
    
    pub fn is_authorized(&self, chat_id: &str) -> bool {
        if self.authorized_ids.is_empty() {
            return true; // Allow all if no restrictions
        }
        self.authorized_ids.contains(&chat_id.to_string())
    }
    
    pub async fn send_message(&self, chat_id: &str, message: &str) -> Result<(), RequestError> {
        let chat_id = ChatId(chat_id.parse::<i64>().unwrap_or(0));
        self.bot.send_message(chat_id, message).await?;
        Ok(())
    }
    
    pub async fn send_alert(&mut self, chat_id: &str, message: &str) -> Result<(), RequestError> {
        let now = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_secs();
        
        // Check cooldown
        if let Some(&last_time) = self.last_alert_time.get(chat_id) {
            if now - last_time < 60 { // 60 seconds cooldown
                return Ok(()); // Skip alert
            }
        }
        
        self.send_message(chat_id, message).await?;
        self.last_alert_time.insert(chat_id.to_string(), now);
        
        Ok(())
    }
    
    pub async fn handle_command(&self, message: &Message, system_data: &crate::SystemData, pc_name: &str) -> Result<(), RequestError> {
        if let Some(text) = message.text() {
            let chat_id = message.chat.id.to_string();
            
            if !self.is_authorized(&chat_id) {
                self.send_message(&chat_id, "🚫 Bạn không có quyền sử dụng bot này.").await?;
                return Ok(());
            }
            
            match text {
                "/start" => {
                    let response = self.format_status_message(system_data, pc_name);
                    self.send_message(&chat_id, &response).await?;
                }
                _ => {
                    // Unknown command
                }
            }
        }
        
        Ok(())
    }
    
    fn format_status_message(&self, data: &crate::SystemData, pc_name: &str) -> String {
        let cpu_temp = data.cpu_temp.map_or("N/A".to_string(), |t| format!("{:.1}°C", t));
        let disk_temp = data.disk_temp.map_or("N/A".to_string(), |t| format!("{:.1}°C", t));
        
        let cpu_temp_icon = if data.cpu_temp.unwrap_or(0.0) >= 80.0 { "🔴" } else { "✅" };
        let disk_temp_icon = if data.disk_temp.unwrap_or(0.0) >= 50.0 { "🔴" } else { "✅" };
        let cpu_usage_icon = if data.cpu_usage >= 85.0 { "🔴" } else { "✅" };
        let ram_usage_icon = if data.ram_usage >= 90.0 { "🔴" } else { "✅" };
        let disk_usage_icon = if data.disk_usage >= 95.0 { "🔴" } else { "✅" };
        
        format!(
            "💻 {} | {}\nTemp: {} CPU {} | {} Disk {}\nUsage: {} CPU {:.0}% | {} RAM {:.0}% | {} Disk {:.0}%",
            pc_name,
            chrono::Utc::now().format("%Y-%m-%d %H:%M:%S"),
            cpu_temp_icon, cpu_temp,
            disk_temp_icon, disk_temp,
            cpu_usage_icon, data.cpu_usage,
            ram_usage_icon, data.ram_usage,
            disk_usage_icon, data.disk_usage
        )
    }
}
